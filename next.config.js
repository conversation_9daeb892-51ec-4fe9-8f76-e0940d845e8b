/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },

  // Minimal optimizations for faster builds
  experimental: {
    optimizePackageImports: [
      '@tabler/icons-react',
      'lucide-react'
    ],
  },

  // Vercel build optimizations
  swcMinify: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Simplified webpack optimizations for faster builds
  webpack: (config, { dev }) => {
    // Only apply minimal optimizations to avoid slowing down builds
    if (!dev) {
      // Simple optimization to reduce bundle size without complex splitting
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    return config;
  },

};

module.exports = nextConfig;
