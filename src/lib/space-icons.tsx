// Import commonly used icons directly to avoid dynamic imports causing re-renders
import {
  IconClipboard,
  IconHome,
  IconBriefcase,
  IconHeart,
  IconStar,
  IconTarget,
  IconBook,
  IconCamera,
  IconMusic,
  IconCar,
  IconPlane,
  IconMountain,
  IconTree,
  IconSun,
  IconMoon,
  IconCloud,
  IconSnowflake,
  IconLeaf,
  IconRocket,
  IconPlanet
} from "@tabler/icons-react";

// Tabler icons for spaces - curated selection for personal expression
export const SPACE_ICONS = [
  // Work & Productivity
  "briefcase", "clipboard", "folder", "file-text", "chart-bar", "target", "bulb", "rocket",

  // Creative & Hobbies
  "palette", "brush", "camera", "music", "headphones", "microphone", "video", "photo",
  "paint", "pencil", "wand", "sparkles",

  // Home & Personal
  "home", "heart", "user", "users", "family", "baby-carriage", "paw", "plant",
  "coffee", "pizza", "chef-hat", "book", "books",

  // Travel & Adventure
  "plane", "car", "bike", "map", "compass", "mountain", "beach", "tent",
  "backpack", "luggage", "world", "location",

  // Health & Fitness
  "run", "swimming", "dumbbell", "yoga", "heart-rate-monitor", "apple",

  // Learning & Growth
  "school", "certificate", "trophy", "medal", "star", "flame", "trending-up",

  // Fun & Entertainment
  "device-gamepad", "puzzle", "dice", "balloon", "gift", "cake", "party-popper",

  // Nature & Outdoors
  "tree", "leaf", "flower", "sun", "moon", "cloud", "snowflake"
];

// Static icon mapping for commonly used icons
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  'clipboard': IconClipboard,
  'home': IconHome,
  'briefcase': IconBriefcase,
  'heart': IconHeart,
  'star': IconStar,
  'target': IconTarget,
  'book': IconBook,
  'camera': IconCamera,
  'music': IconMusic,
  'car': IconCar,
  'plane': IconPlane,
  'mountain': IconMountain,
  'tree': IconTree,
  'sun': IconSun,
  'moon': IconMoon,
  'cloud': IconCloud,
  'snowflake': IconSnowflake,
  'leaf': IconLeaf,
  'rocket': IconRocket,
  'planet': IconPlanet,
};

// Helper function to render Tabler icons
export const renderSpaceIcon = (iconName: string, className?: string) => {
  const IconComponent = iconMap[iconName];

  if (IconComponent) {
    return <IconComponent className={className || "h-5 w-5"} />;
  }

  // Fallback to clipboard icon for unmapped icons
  return <IconClipboard className={className || "h-5 w-5"} />;
};
