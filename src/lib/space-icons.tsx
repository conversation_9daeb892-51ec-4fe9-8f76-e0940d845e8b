// Import commonly used icons directly to avoid dynamic imports causing re-renders
import {
  IconClipboard,
  IconHome,
  IconBriefcase,
  IconHeart,
  IconStar,
  IconTarget,
  IconBook,
  IconCamera,
  IconMusic,
  IconCar,
  IconPlane,
  IconMountain,
  IconTree,
  IconSun,
  IconMoon,
  IconCloud,
  IconSnowflake,
  IconLeaf,
  IconRocket,
  IconPlanet,
  IconFolder,
  IconFileText,
  IconChartBar,
  IconBulb,
  IconPalette,
  IconBrush,
  IconHeadphones,
  IconMicrophone,
  IconVideo,
  IconPhoto,
  IconPencil,
  IconSparkles,
  IconUser,
  IconUsers,
  IconCoffee,
  IconPizza,
  IconBooks,
  IconBike,
  IconMap,
  IconCompass,
  IconTent,
  IconBackpack,
  IconWorld,
  IconLocation,
  IconRun,
  IconApple,
  IconSchool,
  IconCertificate,
  IconTrophy,
  IconMedal,
  IconFlame,
  IconTrendingUp,
  IconDeviceGamepad2,
  IconPuzzle,
  IconDice,
  IconBalloon,
  IconGift,
  IconCake,
  IconFlower
} from "@tabler/icons-react";

// Tabler icons for spaces - curated selection for personal expression
export const SPACE_ICONS = [
  // Work & Productivity
  "briefcase", "clipboard", "folder", "file-text", "chart-bar", "target", "bulb", "rocket",

  // Creative & Hobbies
  "palette", "brush", "camera", "music", "headphones", "microphone", "video", "photo",
  "paint", "pencil", "wand", "sparkles",

  // Home & Personal
  "home", "heart", "user", "users", "family", "baby-carriage", "paw", "plant",
  "coffee", "pizza", "chef-hat", "book", "books",

  // Travel & Adventure
  "plane", "car", "bike", "map", "compass", "mountain", "beach", "tent",
  "backpack", "luggage", "world", "location",

  // Health & Fitness
  "run", "swimming", "dumbbell", "yoga", "heart-rate-monitor", "apple",

  // Learning & Growth
  "school", "certificate", "trophy", "medal", "star", "flame", "trending-up",

  // Fun & Entertainment
  "device-gamepad", "puzzle", "dice", "balloon", "gift", "cake", "party-popper",

  // Nature & Outdoors
  "tree", "leaf", "flower", "sun", "moon", "cloud", "snowflake"
];

// Static icon mapping for commonly used icons
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  // Work & Productivity
  'briefcase': IconBriefcase,
  'clipboard': IconClipboard,
  'folder': IconFolder,
  'file-text': IconFileText,
  'chart-bar': IconChartBar,
  'target': IconTarget,
  'bulb': IconBulb,
  'rocket': IconRocket,

  // Creative & Hobbies
  'palette': IconPalette,
  'brush': IconBrush,
  'camera': IconCamera,
  'music': IconMusic,
  'headphones': IconHeadphones,
  'microphone': IconMicrophone,
  'video': IconVideo,
  'photo': IconPhoto,
  'pencil': IconPencil,
  'sparkles': IconSparkles,

  // Home & Personal
  'home': IconHome,
  'heart': IconHeart,
  'user': IconUser,
  'users': IconUsers,
  'coffee': IconCoffee,
  'pizza': IconPizza,
  'book': IconBook,
  'books': IconBooks,

  // Travel & Adventure
  'plane': IconPlane,
  'car': IconCar,
  'bike': IconBike,
  'map': IconMap,
  'compass': IconCompass,
  'mountain': IconMountain,
  'tent': IconTent,
  'backpack': IconBackpack,
  'world': IconWorld,
  'location': IconLocation,

  // Health & Fitness
  'run': IconRun,
  'apple': IconApple,

  // Learning & Growth
  'school': IconSchool,
  'certificate': IconCertificate,
  'trophy': IconTrophy,
  'medal': IconMedal,
  'star': IconStar,
  'flame': IconFlame,
  'trending-up': IconTrendingUp,

  // Fun & Entertainment
  'device-gamepad': IconDeviceGamepad2,
  'puzzle': IconPuzzle,
  'dice': IconDice,
  'balloon': IconBalloon,
  'gift': IconGift,
  'cake': IconCake,

  // Nature & Outdoors
  'tree': IconTree,
  'leaf': IconLeaf,
  'flower': IconFlower,
  'sun': IconSun,
  'moon': IconMoon,
  'cloud': IconCloud,
  'snowflake': IconSnowflake,
  'planet': IconPlanet,
};

// Helper function to render Tabler icons
export const renderSpaceIcon = (iconName: string, className?: string) => {
  // Skip emoji icons (they contain non-ASCII characters)
  if (iconName && /[^\x00-\x7F]/.test(iconName)) {
    return <IconClipboard className={className || "h-5 w-5"} />;
  }

  const IconComponent = iconMap[iconName];

  if (IconComponent) {
    return <IconComponent className={className || "h-5 w-5"} />;
  }

  // For unmapped icons, try to dynamically import them
  // Convert kebab-case to PascalCase for Tabler icon component names
  const componentName = iconName
    ?.split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');

  // Use a simple dynamic import approach for unmapped icons
  try {
    const TablerIcons = require("@tabler/icons-react");
    const DynamicIcon = TablerIcons[`Icon${componentName}`];

    if (DynamicIcon) {
      return <DynamicIcon className={className || "h-5 w-5"} />;
    }
  } catch {
    // Ignore errors and fall back
  }

  // Final fallback to clipboard icon
  return <IconClipboard className={className || "h-5 w-5"} />;
};
